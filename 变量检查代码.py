# 变量检查代码 - 用于调试和检查当前环境中的变量

print("=== 检查当前环境中的变量 ===")

# 检查所有相关变量
variables_to_check = [
    'df', 'feature_cols', 'scaler',
    'X_train_stage', 'X_test_stage', 'y_train_stage', 'y_test_stage',
    'X_train_rate', 'X_test_rate', 'y_train_rate', 'y_test_rate',
    'stage_results', 'rate_results',
    'stage_performance', 'rate_performance',
    'feature_importance_stage', 'feature_importance_rate'
]

print("变量存在状态:")
for var in variables_to_check:
    exists = var in globals()
    print(f"  {var}: {'✓' if exists else '✗'}")

print("\n=== 详细信息 ===")

# 如果stage_results存在，显示其内容
if 'stage_results' in globals():
    print(f"stage_results 包含的模型: {list(stage_results.keys())}")
else:
    print("stage_results 不存在")

# 如果rate_results存在，显示其内容
if 'rate_results' in globals():
    print(f"rate_results 包含的模型: {list(rate_results.keys())}")
else:
    print("rate_results 不存在")

# 如果stage_performance存在，显示其内容
if 'stage_performance' in globals():
    print("stage_performance 内容:")
    print(stage_performance)
else:
    print("stage_performance 不存在")

# 如果rate_performance存在，显示其内容
if 'rate_performance' in globals():
    print("rate_performance 内容:")
    print(rate_performance)
else:
    print("rate_performance 不存在")

print("\n=== 建议 ===")
missing_vars = [var for var in variables_to_check if var not in globals()]
if missing_vars:
    print("缺失的变量:")
    for var in missing_vars:
        print(f"  - {var}")
    print("\n请按顺序运行以下代码段:")
    if 'df' not in globals():
        print("  1. 第一部分：数据导入")
    if 'feature_cols' not in globals():
        print("  2. 第五部分：数据预处理")
    if 'stage_results' not in globals():
        print("  3. 第七部分：CKD分期预测模型训练")
    if 'rate_results' not in globals():
        print("  4. 第八部分：CKD分层预测模型训练")
    if 'feature_importance_stage' not in globals():
        print("  5. 第九部分：特征重要性分析")
else:
    print("✓ 所有变量都存在，可以运行第十部分代码")

# 如果第八部分已运行但第十部分仍有问题，提供直接解决方案
if 'rate_results' in globals() and 'stage_results' in globals():
    print("\n=== 直接创建缺失的变量 ===")
    
    # 创建performance表（如果不存在）
    if 'stage_performance' not in globals():
        stage_performance = pd.DataFrame({
            'Model': list(stage_results.keys()),
            'Accuracy': [result['accuracy'] for result in stage_results.values()]
        })
        print("✓ 已创建 stage_performance")
    
    if 'rate_performance' not in globals():
        rate_performance = pd.DataFrame({
            'Model': list(rate_results.keys()),
            'Accuracy': [result['accuracy'] for result in rate_results.values()]
        })
        print("✓ 已创建 rate_performance")
    
    # 创建特征重要性（如果不存在）
    if 'feature_importance_stage' not in globals() and 'Random Forest' in stage_results:
        import pandas as pd
        rf_model = stage_results['Random Forest']['model']
        feature_importance_stage = pd.DataFrame({
            'Feature': feature_cols,
            'Importance': rf_model.feature_importances_
        }).sort_values('Importance', ascending=False)
        print("✓ 已创建 feature_importance_stage")
    
    if 'feature_importance_rate' not in globals() and 'Random Forest' in rate_results:
        rf_model = rate_results['Random Forest']['model']
        feature_importance_rate = pd.DataFrame({
            'Feature': feature_cols,
            'Importance': rf_model.feature_importances_
        }).sort_values('Importance', ascending=False)
        print("✓ 已创建 feature_importance_rate")
    
    print("\n现在可以运行第十部分的代码了！")
