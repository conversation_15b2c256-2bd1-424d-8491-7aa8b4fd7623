# 慢性肾病状态预测分析 - 优化版
# 采用面向对象设计，更多模型，更全面的评估

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression, RidgeClassifier
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import (classification_report, confusion_matrix, accuracy_score,
                           precision_score, recall_score, f1_score, roc_auc_score, roc_curve)
from sklearn.feature_selection import SelectKBest, f_classif, RFE
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class CKDPredictor:
    """慢性肾病预测分析类"""

    def __init__(self):
        self.data = None
        self.models = {}
        self.results = {}
        self.feature_importance = {}
        self.scaler = None
        self.feature_selector = None

    def load_data(self, data_path, kidney_path):
        """加载数据"""
        try:
            self.data_clean = pd.read_csv(data_path)
            self.kidney_clean = pd.read_csv(kidney_path)
            print("✓ 数据加载成功")
            print(f"  - 原始数据: {self.data_clean.shape}")
            print(f"  - 清洗数据: {self.kidney_clean.shape}")
            return True
        except Exception as e:
            print(f"✗ 数据加载失败: {e}")
            return False

    def explore_data(self):
        """数据探索分析"""
        print("\n" + "="*50)
        print("数据探索分析")
        print("="*50)

        df = self.data_clean.copy()

        # 基本统计信息
        print("\n1. 数据基本信息:")
        print(f"   - 样本数量: {len(df)}")
        print(f"   - 特征数量: {df.shape[1]}")
        print(f"   - 缺失值总数: {df.isnull().sum().sum()}")

        # 目标变量分布
        print("\n2. 目标变量分布:")
        stage_dist = df['stage'].value_counts().sort_index()
        rate_dist = df['rate'].value_counts()
        print("   CKD分期分布:")
        for stage, count in stage_dist.items():
            print(f"     CKD{stage}: {count} ({count/len(df)*100:.1f}%)")

        print("   CKD分层分布:")
        for rate, count in rate_dist.items():
            print(f"     {rate}: {count} ({count/len(df)*100:.1f}%)")

        # 可视化目标变量分布
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # CKD分期分布
        stage_dist.plot(kind='bar', ax=axes[0,0], color='skyblue', alpha=0.8)
        axes[0,0].set_title('CKD分期分布', fontsize=14, fontweight='bold')
        axes[0,0].set_xlabel('CKD分期')
        axes[0,0].set_ylabel('患者数量')
        axes[0,0].tick_params(axis='x', rotation=0)

        # CKD分层分布
        rate_dist.plot(kind='bar', ax=axes[0,1], color='lightcoral', alpha=0.8)
        axes[0,1].set_title('CKD分层分布', fontsize=14, fontweight='bold')
        axes[0,1].set_xlabel('风险分层')
        axes[0,1].set_ylabel('患者数量')
        axes[0,1].tick_params(axis='x', rotation=45)

        # 性别与CKD分期关系
        gender_stage = pd.crosstab(df['gender'], df['stage'])
        gender_stage.index = ['女性', '男性']
        gender_stage.plot(kind='bar', stacked=True, ax=axes[1,0], alpha=0.8)
        axes[1,0].set_title('性别与CKD分期关系', fontsize=14, fontweight='bold')
        axes[1,0].set_xlabel('性别')
        axes[1,0].set_ylabel('患者数量')
        axes[1,0].tick_params(axis='x', rotation=0)
        axes[1,0].legend(title='CKD分期')

        # 关键指标相关性
        key_indicators = ['Scr', 'eGFR', 'URC_HP', 'ACR']
        corr_matrix = df[key_indicators + ['stage']].corr()
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                   ax=axes[1,1], square=True)
        axes[1,1].set_title('关键指标相关性', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.show()

        return df

    def prepare_data(self, target_type='stage'):
        """数据预处理"""
        print(f"\n准备{target_type}预测数据...")

        # 使用清洗后的数据
        df = self.kidney_clean.copy()

        # 分离特征和目标变量
        feature_cols = [col for col in df.columns if col not in ['stage', 'rate']]
        X = df[feature_cols]
        y = df[target_type]

        print(f"  - 特征数量: {len(feature_cols)}")
        print(f"  - 目标变量: {target_type}")
        print(f"  - 样本数量: {len(X)}")

        # 数据标准化 - 使用RobustScaler更好地处理异常值
        self.scaler = RobustScaler()
        X_scaled = self.scaler.fit_transform(X)

        # 特征选择 - 选择最重要的特征
        self.feature_selector = SelectKBest(score_func=f_classif, k=min(15, len(feature_cols)))
        X_selected = self.feature_selector.fit_transform(X_scaled, y)

        selected_features = np.array(feature_cols)[self.feature_selector.get_support()]
        print(f"  - 选择特征数量: {len(selected_features)}")

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_selected, y, test_size=0.25, random_state=42, stratify=y
        )

        print(f"  - 训练集: {X_train.shape}")
        print(f"  - 测试集: {X_test.shape}")

        return X_train, X_test, y_train, y_test, selected_features

    def define_models(self):
        """定义多种预测模型"""
        self.models = {
            'Random Forest': RandomForestClassifier(
                n_estimators=100, random_state=42, max_depth=10
            ),
            'Gradient Boosting': GradientBoostingClassifier(
                n_estimators=100, random_state=42, max_depth=6
            ),
            'Extra Trees': ExtraTreesClassifier(
                n_estimators=100, random_state=42, max_depth=10
            ),
            'Logistic Regression': LogisticRegression(
                random_state=42, max_iter=1000, C=1.0
            ),
            'SVM': SVC(
                random_state=42, probability=True, kernel='rbf', C=1.0
            ),
            'K-Nearest Neighbors': KNeighborsClassifier(
                n_neighbors=5, weights='distance'
            ),
            'Naive Bayes': GaussianNB(),
            'Decision Tree': DecisionTreeClassifier(
                random_state=42, max_depth=8, min_samples_split=10
            ),
            'Neural Network': MLPClassifier(
                random_state=42, max_iter=500, hidden_layer_sizes=(100, 50)
            ),
            'Ridge Classifier': RidgeClassifier(
                random_state=42, alpha=1.0
            )
        }

        print(f"\n定义了 {len(self.models)} 个预测模型:")
        for i, name in enumerate(self.models.keys(), 1):
            print(f"  {i:2d}. {name}")

    def train_and_evaluate(self, X_train, X_test, y_train, y_test, target_type):
        """训练和评估模型"""
        print(f"\n开始训练{target_type}预测模型...")

        results = {}

        for name, model in self.models.items():
            print(f"\n训练 {name}...")

            try:
                # 训练模型
                model.fit(X_train, y_train)

                # 预测
                y_pred = model.predict(X_test)
                y_pred_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None

                # 计算评估指标
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
                recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
                f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)

                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')

                # AUC (仅适用于二分类或可以计算的多分类)
                auc_score = None
                if y_pred_proba is not None and len(np.unique(y_test)) == 2:
                    auc_score = roc_auc_score(y_test, y_pred_proba[:, 1])

                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'auc': auc_score,
                    'y_pred': y_pred,
                    'y_pred_proba': y_pred_proba,
                    'y_test': y_test
                }

                print(f"  准确率: {accuracy:.4f}")
                print(f"  交叉验证: {cv_scores.mean():.4f} (±{cv_scores.std():.4f})")

            except Exception as e:
                print(f"  ✗ 训练失败: {e}")
                continue

        return results

    def analyze_feature_importance(self, results, feature_names, target_type):
        """分析特征重要性"""
        print(f"\n分析{target_type}预测的特征重要性...")

        importance_data = {}

        # 从树模型中提取特征重要性
        tree_models = ['Random Forest', 'Gradient Boosting', 'Extra Trees', 'Decision Tree']

        for model_name in tree_models:
            if model_name in results:
                model = results[model_name]['model']
                if hasattr(model, 'feature_importances_'):
                    importance_data[model_name] = model.feature_importances_

        if importance_data:
            # 创建特征重要性DataFrame
            importance_df = pd.DataFrame(importance_data, index=feature_names)
            importance_df['Average'] = importance_df.mean(axis=1)
            importance_df = importance_df.sort_values('Average', ascending=False)

            print(f"Top 10 重要特征:")
            for i, (feature, avg_importance) in enumerate(importance_df['Average'].head(10).items(), 1):
                print(f"  {i:2d}. {feature}: {avg_importance:.4f}")

            return importance_df

        return None

    def visualize_results(self, results, target_type):
        """可视化结果"""
        print(f"\n生成{target_type}预测结果可视化...")

        # 创建性能比较DataFrame
        performance_data = []
        for name, result in results.items():
            performance_data.append({
                'Model': name,
                'Accuracy': result['accuracy'],
                'Precision': result['precision'],
                'Recall': result['recall'],
                'F1-Score': result['f1_score'],
                'CV_Mean': result['cv_mean']
            })

        performance_df = pd.DataFrame(performance_data)
        performance_df = performance_df.sort_values('Accuracy', ascending=False)

        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 模型准确率比较
        bars = axes[0,0].bar(range(len(performance_df)), performance_df['Accuracy'],
                            alpha=0.8, color='skyblue')
        axes[0,0].set_title(f'{target_type}预测模型准确率比较', fontsize=14, fontweight='bold')
        axes[0,0].set_xlabel('模型')
        axes[0,0].set_ylabel('准确率')
        axes[0,0].set_xticks(range(len(performance_df)))
        axes[0,0].set_xticklabels(performance_df['Model'], rotation=45, ha='right')
        axes[0,0].set_ylim(0, 1)

        # 添加数值标签
        for bar, acc in zip(bars, performance_df['Accuracy']):
            axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                          f'{acc:.3f}', ha='center', va='bottom', fontsize=10)

        # 2. 多指标雷达图（选择前5个模型）
        top5_models = performance_df.head(5)
        metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'CV_Mean']

        angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形

        ax_radar = plt.subplot(2, 2, 2, projection='polar')
        colors = plt.cm.Set3(np.linspace(0, 1, len(top5_models)))

        for i, (_, row) in enumerate(top5_models.iterrows()):
            values = [row[metric] for metric in metrics]
            values += values[:1]  # 闭合图形

            ax_radar.plot(angles, values, 'o-', linewidth=2,
                         label=row['Model'], color=colors[i])
            ax_radar.fill(angles, values, alpha=0.25, color=colors[i])

        ax_radar.set_xticks(angles[:-1])
        ax_radar.set_xticklabels(metrics)
        ax_radar.set_ylim(0, 1)
        ax_radar.set_title(f'{target_type}预测Top5模型性能雷达图',
                          fontsize=12, fontweight='bold', pad=20)
        ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

        # 3. 最佳模型混淆矩阵
        best_model_name = performance_df.iloc[0]['Model']
        best_result = results[best_model_name]

        cm = confusion_matrix(best_result['y_test'], best_result['y_pred'])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1,0])
        axes[1,0].set_title(f'最佳模型混淆矩阵\n({best_model_name})',
                           fontsize=12, fontweight='bold')
        axes[1,0].set_xlabel('预测值')
        axes[1,0].set_ylabel('真实值')

        # 4. 模型性能分布箱线图
        metrics_for_box = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
        box_data = [performance_df[metric].values for metric in metrics_for_box]

        bp = axes[1,1].boxplot(box_data, labels=metrics_for_box, patch_artist=True)
        for patch, color in zip(bp['boxes'], ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow']):
            patch.set_facecolor(color)

        axes[1,1].set_title(f'{target_type}预测性能指标分布', fontsize=12, fontweight='bold')
        axes[1,1].set_ylabel('分数')
        axes[1,1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.show()

        return performance_df

    def generate_predictions(self, results, target_type, n_samples=10):
        """生成预测示例"""
        print(f"\n生成{target_type}预测示例...")

        # 获取最佳模型
        best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
        best_result = results[best_model_name]

        # 选择样本进行预测展示
        y_test = best_result['y_test']
        y_pred = best_result['y_pred']

        # 随机选择样本
        indices = np.random.choice(len(y_test), min(n_samples, len(y_test)), replace=False)

        prediction_data = []
        for i, idx in enumerate(indices):
            prediction_data.append({
                '样本': i+1,
                f'实际{target_type}': y_test.iloc[idx] if hasattr(y_test, 'iloc') else y_test[idx],
                f'预测{target_type}': y_pred[idx],
                '预测正确': '✓' if (y_test.iloc[idx] if hasattr(y_test, 'iloc') else y_test[idx]) == y_pred[idx] else '✗'
            })

        prediction_df = pd.DataFrame(prediction_data)

        # 计算准确率
        correct_predictions = sum(prediction_df['预测正确'] == '✓')
        accuracy = correct_predictions / len(prediction_df) * 100

        print(f"使用最佳模型 ({best_model_name}) 的预测示例:")
        print(prediction_df.to_string(index=False))
        print(f"\n示例准确率: {correct_predictions}/{len(prediction_df)} ({accuracy:.1f}%)")

        return prediction_df

    def save_results(self, stage_results, rate_results, stage_performance, rate_performance):
        """保存结果"""
        print("\n保存分析结果...")

        try:
            import pickle

            # 保存模型
            models_to_save = {
                'stage_models': {name: result['model'] for name, result in stage_results.items()},
                'rate_models': {name: result['model'] for name, result in rate_results.items()},
                'scaler': self.scaler,
                'feature_selector': self.feature_selector
            }

            with open('ckd_models_optimized.pkl', 'wb') as f:
                pickle.dump(models_to_save, f)

            # 保存性能结果
            results_to_save = {
                'stage_performance': stage_performance,
                'rate_performance': rate_performance,
                'stage_feature_importance': self.feature_importance.get('stage'),
                'rate_feature_importance': self.feature_importance.get('rate')
            }

            with open('ckd_results_optimized.pkl', 'wb') as f:
                pickle.dump(results_to_save, f)

            print("✓ 结果保存成功:")
            print("  - ckd_models_optimized.pkl: 训练好的模型")
            print("  - ckd_results_optimized.pkl: 分析结果")

        except Exception as e:
            print(f"✗ 保存失败: {e}")

    def run_complete_analysis(self):
        """运行完整分析流程"""
        print("="*60)
        print("慢性肾病状态预测分析 - 优化版")
        print("="*60)

        # 1. 加载数据
        if not self.load_data('可参考选题1数据-慢性肾病状态预测/data_clean.csv',
                             '可参考选题1数据-慢性肾病状态预测/kidney_clean.csv'):
            return

        # 2. 数据探索
        self.explore_data()

        # 3. 定义模型
        self.define_models()

        # 4. CKD分期预测
        print("\n" + "="*50)
        print("CKD分期预测分析")
        print("="*50)

        X_train_stage, X_test_stage, y_train_stage, y_test_stage, stage_features = self.prepare_data('stage')
        stage_results = self.train_and_evaluate(X_train_stage, X_test_stage, y_train_stage, y_test_stage, 'stage')
        stage_importance = self.analyze_feature_importance(stage_results, stage_features, 'stage')
        stage_performance = self.visualize_results(stage_results, 'CKD分期')
        self.generate_predictions(stage_results, 'CKD分期')

        # 5. CKD分层预测
        print("\n" + "="*50)
        print("CKD分层预测分析")
        print("="*50)

        X_train_rate, X_test_rate, y_train_rate, y_test_rate, rate_features = self.prepare_data('rate')
        rate_results = self.train_and_evaluate(X_train_rate, X_test_rate, y_train_rate, y_test_rate, 'rate')
        rate_importance = self.analyze_feature_importance(rate_results, rate_features, 'rate')
        rate_performance = self.visualize_results(rate_results, 'CKD分层')
        self.generate_predictions(rate_results, 'CKD分层')

        # 6. 保存特征重要性
        self.feature_importance = {
            'stage': stage_importance,
            'rate': rate_importance
        }

        # 7. 保存结果
        self.save_results(stage_results, rate_results, stage_performance, rate_performance)

        # 8. 生成总结报告
        self.generate_summary_report(stage_performance, rate_performance)

        return stage_results, rate_results, stage_performance, rate_performance

    def generate_summary_report(self, stage_performance, rate_performance):
        """生成总结报告"""
        print("\n" + "="*60)
        print("分析总结报告")
        print("="*60)

        print("\n📊 模型性能总结:")
        print(f"  CKD分期预测最佳模型: {stage_performance.iloc[0]['Model']} "
              f"(准确率: {stage_performance.iloc[0]['Accuracy']:.4f})")
        print(f"  CKD分层预测最佳模型: {rate_performance.iloc[0]['Model']} "
              f"(准确率: {rate_performance.iloc[0]['Accuracy']:.4f})")

        print(f"\n  CKD分期预测平均准确率: {stage_performance['Accuracy'].mean():.4f}")
        print(f"  CKD分层预测平均准确率: {rate_performance['Accuracy'].mean():.4f}")

        print("\n🔍 关键发现:")
        if self.feature_importance.get('stage') is not None:
            top_stage_feature = self.feature_importance['stage']['Average'].index[0]
            print(f"  - CKD分期预测最重要特征: {top_stage_feature}")

        if self.feature_importance.get('rate') is not None:
            top_rate_feature = self.feature_importance['rate']['Average'].index[0]
            print(f"  - CKD分层预测最重要特征: {top_rate_feature}")

        print("\n💡 建议:")
        print("  1. 重点监测特征重要性排名前5的指标")
        print("  2. 对于高风险患者，建议使用集成模型进行预测")
        print("  3. 定期重新训练模型以保持预测准确性")
        print("  4. 结合临床专家知识进一步优化特征选择")

        print("\n✅ 分析完成！所有结果已保存。")


# 使用示例
if __name__ == "__main__":
    # 创建预测器实例
    predictor = CKDPredictor()

    # 运行完整分析
    stage_results, rate_results, stage_performance, rate_performance = predictor.run_complete_analysis()
