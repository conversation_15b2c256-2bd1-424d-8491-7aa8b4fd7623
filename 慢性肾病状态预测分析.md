# 慢性肾病状态预测数据挖掘分析

## 第一部分：数据导入和基础探索

```python
# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import roc_curve, auc
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("库导入完成")
```

```python
# 读取数据
data_clean = pd.read_csv('可参考选题1数据-慢性肾病状态预测/data_clean.csv')
kidney_clean = pd.read_csv('可参考选题1数据-慢性肾病状态预测/kidney_clean.csv')

print("数据文件读取完成")
print(f"data_clean数据形状: {data_clean.shape}")
print(f"kidney_clean数据形状: {kidney_clean.shape}")
```

```python
# 查看数据基本信息
print("=== data_clean数据基本信息 ===")
print(data_clean.info())
print("\n=== data_clean前5行数据 ===")
print(data_clean.head())
```

```python
print("=== kidney_clean数据基本信息 ===")
print(kidney_clean.info())
print("\n=== kidney_clean前5行数据 ===")
print(kidney_clean.head())
```

```python
# 检查缺失值
print("=== data_clean缺失值情况 ===")
print(data_clean.isnull().sum())
print("\n=== kidney_clean缺失值情况 ===")
print(kidney_clean.isnull().sum())
```

## 第二部分：数据探索性分析

```python
# 选择主要数据集进行分析（使用data_clean）
df = data_clean.copy()

# 查看目标变量分布
print("=== CKD分期(stage)分布 ===")
print(df['stage'].value_counts().sort_index())

print("\n=== CKD分层(rate)分布 ===")
print(df['rate'].value_counts())
```

```python
# 可视化目标变量分布
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# CKD分期分布
stage_counts = df['stage'].value_counts().sort_index()
axes[0].bar(stage_counts.index, stage_counts.values, color='skyblue', alpha=0.7)
axes[0].set_title('CKD分期分布', fontsize=14)
axes[0].set_xlabel('CKD分期')
axes[0].set_ylabel('患者数量')
for i, v in enumerate(stage_counts.values):
    axes[0].text(stage_counts.index[i], v + 5, str(v), ha='center')

# CKD分层分布
rate_counts = df['rate'].value_counts()
rate_labels = ['低危', '中危', '高危', '极高危']
axes[1].bar(range(len(rate_counts)), rate_counts.values, color='lightcoral', alpha=0.7)
axes[1].set_title('CKD分层分布', fontsize=14)
axes[1].set_xlabel('风险分层')
axes[1].set_ylabel('患者数量')
axes[1].set_xticks(range(len(rate_counts)))
axes[1].set_xticklabels(rate_labels)
for i, v in enumerate(rate_counts.values):
    axes[1].text(i, v + 5, str(v), ha='center')

plt.tight_layout()
plt.show()
```

```python
# 性别分布分析
print("=== 性别分布 ===")
gender_counts = df['gender'].value_counts()
print(f"男性: {gender_counts[1]} 人")
print(f"女性: {gender_counts[0]} 人")

# 性别与CKD分期的关系
print("\n=== 性别与CKD分期交叉表 ===")
gender_stage_crosstab = pd.crosstab(df['gender'], df['stage'], margins=True)
print(gender_stage_crosstab)
```

```python
# 可视化性别与CKD分期的关系
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# 性别分布饼图
gender_labels = ['女性', '男性']
gender_counts = df['gender'].value_counts().sort_index()
axes[0].pie(gender_counts.values, labels=gender_labels, autopct='%1.1f%%', startangle=90)
axes[0].set_title('性别分布', fontsize=14)

# 性别与CKD分期的堆叠柱状图
gender_stage_df = pd.crosstab(df['gender'], df['stage'])
gender_stage_df.index = ['女性', '男性']
gender_stage_df.plot(kind='bar', stacked=True, ax=axes[1], alpha=0.8)
axes[1].set_title('性别与CKD分期分布', fontsize=14)
axes[1].set_xlabel('性别')
axes[1].set_ylabel('患者数量')
axes[1].legend(title='CKD分期', bbox_to_anchor=(1.05, 1), loc='upper left')
axes[1].tick_params(axis='x', rotation=0)

plt.tight_layout()
plt.show()
```

```python
# 关键生理指标分析
key_indicators = ['Scr', 'eGFR', 'URC_HP', 'ACR']

print("=== 关键生理指标描述性统计 ===")
print(df[key_indicators].describe())
```

```python
# 可视化关键生理指标分布
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for i, indicator in enumerate(key_indicators):
    axes[i].hist(df[indicator], bins=30, alpha=0.7, color='steelblue', edgecolor='black')
    axes[i].set_title(f'{indicator}分布', fontsize=12)
    axes[i].set_xlabel(indicator)
    axes[i].set_ylabel('频数')

    # 添加统计信息
    mean_val = df[indicator].mean()
    median_val = df[indicator].median()
    axes[i].axvline(mean_val, color='red', linestyle='--', label=f'均值: {mean_val:.2f}')
    axes[i].axvline(median_val, color='orange', linestyle='--', label=f'中位数: {median_val:.2f}')
    axes[i].legend()

plt.tight_layout()
plt.show()
```

```python
# 生理指标与CKD分期的关系
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for i, indicator in enumerate(key_indicators):
    # 箱线图显示不同CKD分期的指标分布
    df.boxplot(column=indicator, by='stage', ax=axes[i])
    axes[i].set_title(f'不同CKD分期的{indicator}分布')
    axes[i].set_xlabel('CKD分期')
    axes[i].set_ylabel(indicator)

plt.suptitle('')  # 移除默认标题
plt.tight_layout()
plt.show()
```

## 第三部分：疾病史和风险因素分析

```python
# 分析疾病史相关因素
disease_factors = ['genetic', 'family', 'transplant', 'biopsy', 'HBP', 'diabetes', 'hyperuricemia', 'UAS']

print("=== 疾病史和风险因素统计 ===")
for factor in disease_factors:
    print(f"{factor}: {df[factor].value_counts().to_dict()}")
```

```python
# 可视化疾病史因素
fig, axes = plt.subplots(2, 4, figsize=(20, 10))
axes = axes.ravel()

factor_names = {
    'genetic': '遗传性肾脏病史',
    'family': '慢性肾炎家族史',
    'transplant': '肾移植病史',
    'biopsy': '肾穿刺活检术史',
    'HBP': '高血压病史',
    'diabetes': '糖尿病病史',
    'hyperuricemia': '高尿血酸症',
    'UAS': '肾脏超声异常'
}

for i, factor in enumerate(disease_factors):
    factor_counts = df[factor].value_counts()
    axes[i].bar(factor_counts.index, factor_counts.values, alpha=0.7)
    axes[i].set_title(factor_names[factor], fontsize=12)
    axes[i].set_xlabel('是否患有')
    axes[i].set_ylabel('患者数量')

    # 添加数值标签
    for j, v in enumerate(factor_counts.values):
        axes[i].text(factor_counts.index[j], v + 5, str(v), ha='center')

plt.tight_layout()
plt.show()
```

```python
# 疾病史因素与CKD分期的关联分析
print("=== 疾病史因素与CKD分期的关联分析 ===")

for factor in disease_factors:
    print(f"\n{factor_names[factor]}与CKD分期交叉表:")
    crosstab = pd.crosstab(df[factor], df['stage'], margins=True)
    print(crosstab)

    # 计算患病率
    if 1 in df[factor].values:
        disease_rate = df[factor].sum() / len(df) * 100
        print(f"总体患病率: {disease_rate:.1f}%")
```

## 第四部分：相关性分析

```python
# 计算数值变量之间的相关性
numeric_cols = ['Scr', 'eGFR', 'URC_HP', 'ACR', 'stage', 'rate'] + disease_factors
correlation_matrix = df[numeric_cols].corr()

print("=== 相关性矩阵 ===")
print(correlation_matrix.round(3))
```

```python
# 可视化相关性热力图
plt.figure(figsize=(12, 10))
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
            square=True, linewidths=0.5, cbar_kws={"shrink": .8})
plt.title('变量相关性热力图', fontsize=16)
plt.tight_layout()
plt.show()
```

```python
# 重点分析与CKD分期相关性最强的因素
stage_corr = correlation_matrix['stage'].abs().sort_values(ascending=False)
print("=== 与CKD分期相关性排序 ===")
for var, corr in stage_corr.items():
    if var != 'stage':
        print(f"{var}: {corr:.3f}")
```

```python
# 分析不同医院的CKD分期分布
print("=== 不同医院CKD分期分布 ===")
hospital_stage = pd.crosstab(df['hos_name'], df['stage'], margins=True)
print(hospital_stage)

# 可视化不同医院的CKD分期分布
plt.figure(figsize=(12, 8))
hospital_stage_pct = pd.crosstab(df['hos_name'], df['stage'], normalize='index') * 100
hospital_stage_pct.plot(kind='bar', stacked=True, alpha=0.8)
plt.title('不同医院CKD分期分布比例', fontsize=14)
plt.xlabel('医院')
plt.ylabel('比例 (%)')
plt.legend(title='CKD分期', bbox_to_anchor=(1.05, 1), loc='upper left')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

## 第五部分：数据预处理

```python
# 使用kidney_clean数据进行建模（已经过预处理）
model_df = kidney_clean.copy()

print("=== 建模数据基本信息 ===")
print(model_df.info())
print(f"\n数据形状: {model_df.shape}")
```

```python
# 检查目标变量分布
print("=== 目标变量分布 ===")
print("CKD分期分布:")
print(model_df['stage'].value_counts().sort_index())
print("\nCKD分层分布:")
print(model_df['rate'].value_counts().sort_index())
```

```python
# 准备特征和目标变量
# 选择特征变量（排除目标变量和标识变量）
feature_cols = [col for col in model_df.columns if col not in ['stage', 'rate']]
X = model_df[feature_cols]
y_stage = model_df['stage']  # CKD分期预测
y_rate = model_df['rate']    # CKD分层预测

print("=== 特征变量 ===")
print(f"特征数量: {len(feature_cols)}")
print("特征列表:")
for i, col in enumerate(feature_cols):
    print(f"{i+1:2d}. {col}")
```

```python
# 数据标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
X_scaled_df = pd.DataFrame(X_scaled, columns=feature_cols)

print("=== 数据标准化完成 ===")
print("标准化前后对比:")
print("标准化前:")
print(X.describe().round(3))
print("\n标准化后:")
print(X_scaled_df.describe().round(3))
```

## 第六部分：机器学习建模

```python
# 划分训练集和测试集 - CKD分期预测
X_train_stage, X_test_stage, y_train_stage, y_test_stage = train_test_split(
    X_scaled, y_stage, test_size=0.3, random_state=42, stratify=y_stage
)

print("=== CKD分期预测数据划分 ===")
print(f"训练集大小: {X_train_stage.shape}")
print(f"测试集大小: {X_test_stage.shape}")
print("训练集目标变量分布:")
print(pd.Series(y_train_stage).value_counts().sort_index())
```

```python
# 划分训练集和测试集 - CKD分层预测
X_train_rate, X_test_rate, y_train_rate, y_test_rate = train_test_split(
    X_scaled, y_rate, test_size=0.3, random_state=42, stratify=y_rate
)

print("=== CKD分层预测数据划分 ===")
print(f"训练集大小: {X_train_rate.shape}")
print(f"测试集大小: {X_test_rate.shape}")
print("训练集目标变量分布:")
print(pd.Series(y_train_rate).value_counts().sort_index())
```

```python
# 定义模型
models = {
    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
    'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
    'SVM': SVC(random_state=42, probability=True)
}

print("=== 模型定义完成 ===")
print("模型列表:")
for name in models.keys():
    print(f"- {name}")
```

## 第七部分：CKD分期预测模型训练与评估

```python
# 训练和评估CKD分期预测模型
stage_results = {}

print("=== CKD分期预测模型训练与评估 ===")

for name, model in models.items():
    print(f"\n--- {name} ---")

    # 训练模型
    model.fit(X_train_stage, y_train_stage)

    # 预测
    y_pred_stage = model.predict(X_test_stage)
    y_pred_proba_stage = model.predict_proba(X_test_stage)

    # 计算准确率
    accuracy = accuracy_score(y_test_stage, y_pred_stage)
    print(f"准确率: {accuracy:.4f}")

    # 分类报告
    print("分类报告:")
    print(classification_report(y_test_stage, y_pred_stage))

    # 保存结果
    stage_results[name] = {
        'model': model,
        'accuracy': accuracy,
        'y_pred': y_pred_stage,
        'y_pred_proba': y_pred_proba_stage
    }
```

```python
# 可视化CKD分期预测结果
fig, axes = plt.subplots(1, 3, figsize=(18, 5))

for i, (name, result) in enumerate(stage_results.items()):
    # 混淆矩阵
    cm = confusion_matrix(y_test_stage, result['y_pred'])
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i])
    axes[i].set_title(f'{name}\n准确率: {result["accuracy"]:.4f}')
    axes[i].set_xlabel('预测值')
    axes[i].set_ylabel('真实值')

plt.tight_layout()
plt.show()
```

```python
# 模型性能比较 - CKD分期
stage_performance = pd.DataFrame({
    'Model': list(stage_results.keys()),
    'Accuracy': [result['accuracy'] for result in stage_results.values()]
})

print("=== CKD分期预测模型性能比较 ===")
print(stage_performance.sort_values('Accuracy', ascending=False))

# 可视化模型性能
plt.figure(figsize=(10, 6))
bars = plt.bar(stage_performance['Model'], stage_performance['Accuracy'], alpha=0.7)
plt.title('CKD分期预测模型准确率比较', fontsize=14)
plt.xlabel('模型')
plt.ylabel('准确率')
plt.ylim(0, 1)

# 添加数值标签
for bar, acc in zip(bars, stage_performance['Accuracy']):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
             f'{acc:.4f}', ha='center', va='bottom')

plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

## 第八部分：CKD分层预测模型训练与评估

```python
# 训练和评估CKD分层预测模型
rate_results = {}

print("=== CKD分层预测模型训练与评估 ===")

for name, model in models.items():
    print(f"\n--- {name} ---")

    # 重新初始化模型（避免之前训练的影响）
    if name == 'Random Forest':
        model = RandomForestClassifier(n_estimators=100, random_state=42)
    elif name == 'Logistic Regression':
        model = LogisticRegression(random_state=42, max_iter=1000)
    elif name == 'SVM':
        model = SVC(random_state=42, probability=True)

    # 训练模型
    model.fit(X_train_rate, y_train_rate)

    # 预测
    y_pred_rate = model.predict(X_test_rate)
    y_pred_proba_rate = model.predict_proba(X_test_rate)

    # 计算准确率
    accuracy = accuracy_score(y_test_rate, y_pred_rate)
    print(f"准确率: {accuracy:.4f}")

    # 分类报告
    print("分类报告:")
    print(classification_report(y_test_rate, y_pred_rate))

    # 保存结果
    rate_results[name] = {
        'model': model,
        'accuracy': accuracy,
        'y_pred': y_pred_rate,
        'y_pred_proba': y_pred_proba_rate
    }
```

```python
# 可视化CKD分层预测结果
fig, axes = plt.subplots(1, 3, figsize=(18, 5))

for i, (name, result) in enumerate(rate_results.items()):
    # 混淆矩阵
    cm = confusion_matrix(y_test_rate, result['y_pred'])
    sns.heatmap(cm, annot=True, fmt='d', cmap='Greens', ax=axes[i])
    axes[i].set_title(f'{name}\n准确率: {result["accuracy"]:.4f}')
    axes[i].set_xlabel('预测值')
    axes[i].set_ylabel('真实值')

plt.tight_layout()
plt.show()
```

```python
# 模型性能比较 - CKD分层
rate_performance = pd.DataFrame({
    'Model': list(rate_results.keys()),
    'Accuracy': [result['accuracy'] for result in rate_results.values()]
})

print("=== CKD分层预测模型性能比较 ===")
print(rate_performance.sort_values('Accuracy', ascending=False))

# 可视化模型性能
plt.figure(figsize=(10, 6))
bars = plt.bar(rate_performance['Model'], rate_performance['Accuracy'],
               alpha=0.7, color='lightgreen')
plt.title('CKD分层预测模型准确率比较', fontsize=14)
plt.xlabel('模型')
plt.ylabel('准确率')
plt.ylim(0, 1)

# 添加数值标签
for bar, acc in zip(bars, rate_performance['Accuracy']):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
             f'{acc:.4f}', ha='center', va='bottom')

plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

## 第九部分：特征重要性分析

```python
# 分析随机森林模型的特征重要性 - CKD分期
rf_stage_model = stage_results['Random Forest']['model']
feature_importance_stage = pd.DataFrame({
    'Feature': feature_cols,
    'Importance': rf_stage_model.feature_importances_
}).sort_values('Importance', ascending=False)

print("=== CKD分期预测特征重要性 (随机森林) ===")
print(feature_importance_stage.head(10))
```

```python
# 可视化CKD分期预测特征重要性
plt.figure(figsize=(12, 8))
top_features_stage = feature_importance_stage.head(15)
bars = plt.barh(range(len(top_features_stage)), top_features_stage['Importance'])
plt.yticks(range(len(top_features_stage)), top_features_stage['Feature'])
plt.xlabel('重要性')
plt.title('CKD分期预测特征重要性排序 (Top 15)', fontsize=14)
plt.gca().invert_yaxis()

# 添加数值标签
for i, (bar, importance) in enumerate(zip(bars, top_features_stage['Importance'])):
    plt.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2,
             f'{importance:.3f}', va='center')

plt.tight_layout()
plt.show()
```

```python
# 分析随机森林模型的特征重要性 - CKD分层
rf_rate_model = rate_results['Random Forest']['model']
feature_importance_rate = pd.DataFrame({
    'Feature': feature_cols,
    'Importance': rf_rate_model.feature_importances_
}).sort_values('Importance', ascending=False)

print("=== CKD分层预测特征重要性 (随机森林) ===")
print(feature_importance_rate.head(10))
```

```python
# 可视化CKD分层预测特征重要性
plt.figure(figsize=(12, 8))
top_features_rate = feature_importance_rate.head(15)
bars = plt.barh(range(len(top_features_rate)), top_features_rate['Importance'])
plt.yticks(range(len(top_features_rate)), top_features_rate['Feature'])
plt.xlabel('重要性')
plt.title('CKD分层预测特征重要性排序 (Top 15)', fontsize=14)
plt.gca().invert_yaxis()

# 添加数值标签
for i, (bar, importance) in enumerate(zip(bars, top_features_rate['Importance'])):
    plt.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2,
             f'{importance:.3f}', va='center')

plt.tight_layout()
plt.show()
```

## 第十部分：结果总结与分析

```python
# 综合分析结果
print("=== 慢性肾病状态预测分析总结 ===")
print("\n1. 数据概况:")
print(f"   - 总样本数: {len(df)}")
print(f"   - 特征数量: {len(feature_cols)}")
print(f"   - CKD分期分布: {dict(df['stage'].value_counts().sort_index())}")
print(f"   - CKD分层分布: {dict(df['rate'].value_counts().sort_index())}")

print("\n2. 最佳模型性能:")
# 检查变量是否存在，如果不存在则创建
if 'stage_performance' in locals() or 'stage_performance' in globals():
    best_stage_model = stage_performance.loc[stage_performance['Accuracy'].idxmax()]
    print(f"   - CKD分期预测最佳模型: {best_stage_model['Model']} (准确率: {best_stage_model['Accuracy']:.4f})")
else:
    print("   - CKD分期预测模型结果未找到，请先运行第七部分代码")

if 'rate_performance' in locals() or 'rate_performance' in globals():
    best_rate_model = rate_performance.loc[rate_performance['Accuracy'].idxmax()]
    print(f"   - CKD分层预测最佳模型: {best_rate_model['Model']} (准确率: {best_rate_model['Accuracy']:.4f})")
else:
    print("   - CKD分层预测模型结果未找到，请先运行第八部分代码")

print("\n3. 关键预测因子 (Top 5):")
if 'feature_importance_stage' in locals() or 'feature_importance_stage' in globals():
    print("   CKD分期预测:")
    for i, row in feature_importance_stage.head(5).iterrows():
        print(f"     {row['Feature']}: {row['Importance']:.4f}")
else:
    print("   CKD分期预测特征重要性未找到，请先运行第九部分代码")

if 'feature_importance_rate' in locals() or 'feature_importance_rate' in globals():
    print("   CKD分层预测:")
    for i, row in feature_importance_rate.head(5).iterrows():
        print(f"     {row['Feature']}: {row['Importance']:.4f}")
else:
    print("   CKD分层预测特征重要性未找到，请先运行第九部分代码")
```

```python
# 创建最终的综合可视化报告
fig, axes = plt.subplots(2, 3, figsize=(20, 12))

# 1. CKD分期分布
stage_counts = df['stage'].value_counts().sort_index()
axes[0,0].bar(stage_counts.index, stage_counts.values, color='skyblue', alpha=0.7)
axes[0,0].set_title('CKD分期分布', fontsize=12)
axes[0,0].set_xlabel('CKD分期')
axes[0,0].set_ylabel('患者数量')

# 2. CKD分层分布
rate_counts = df['rate'].value_counts().sort_index()
axes[0,1].bar(range(len(rate_counts)), rate_counts.values, color='lightcoral', alpha=0.7)
axes[0,1].set_title('CKD分层分布', fontsize=12)
axes[0,1].set_xlabel('风险分层')
axes[0,1].set_ylabel('患者数量')
axes[0,1].set_xticks(range(len(rate_counts)))
axes[0,1].set_xticklabels(['低危', '中危', '高危', '极高危'])

# 3. 模型性能比较 - CKD分期
if 'stage_performance' in locals() or 'stage_performance' in globals():
    axes[0,2].bar(stage_performance['Model'], stage_performance['Accuracy'], alpha=0.7)
    axes[0,2].set_title('CKD分期预测模型性能', fontsize=12)
    axes[0,2].set_ylabel('准确率')
    axes[0,2].tick_params(axis='x', rotation=45)
else:
    axes[0,2].text(0.5, 0.5, '请先运行第七部分代码', ha='center', va='center', transform=axes[0,2].transAxes)
    axes[0,2].set_title('CKD分期预测模型性能', fontsize=12)

# 4. 模型性能比较 - CKD分层
if 'rate_performance' in locals() or 'rate_performance' in globals():
    axes[1,0].bar(rate_performance['Model'], rate_performance['Accuracy'],
                  alpha=0.7, color='lightgreen')
    axes[1,0].set_title('CKD分层预测模型性能', fontsize=12)
    axes[1,0].set_ylabel('准确率')
    axes[1,0].tick_params(axis='x', rotation=45)
else:
    axes[1,0].text(0.5, 0.5, '请先运行第八部分代码', ha='center', va='center', transform=axes[1,0].transAxes)
    axes[1,0].set_title('CKD分层预测模型性能', fontsize=12)

# 5. CKD分期预测特征重要性 (Top 10)
if 'feature_importance_stage' in locals() or 'feature_importance_stage' in globals():
    top10_stage = feature_importance_stage.head(10)
    axes[1,1].barh(range(len(top10_stage)), top10_stage['Importance'])
    axes[1,1].set_yticks(range(len(top10_stage)))
    axes[1,1].set_yticklabels(top10_stage['Feature'])
    axes[1,1].set_title('CKD分期预测特征重要性 (Top 10)', fontsize=12)
    axes[1,1].set_xlabel('重要性')
    axes[1,1].invert_yaxis()
else:
    axes[1,1].text(0.5, 0.5, '请先运行第九部分代码', ha='center', va='center', transform=axes[1,1].transAxes)
    axes[1,1].set_title('CKD分期预测特征重要性 (Top 10)', fontsize=12)

# 6. CKD分层预测特征重要性 (Top 10)
if 'feature_importance_rate' in locals() or 'feature_importance_rate' in globals():
    top10_rate = feature_importance_rate.head(10)
    axes[1,2].barh(range(len(top10_rate)), top10_rate['Importance'])
    axes[1,2].set_yticks(range(len(top10_rate)))
    axes[1,2].set_yticklabels(top10_rate['Feature'])
    axes[1,2].set_title('CKD分层预测特征重要性 (Top 10)', fontsize=12)
    axes[1,2].set_xlabel('重要性')
    axes[1,2].invert_yaxis()
else:
    axes[1,2].text(0.5, 0.5, '请先运行第九部分代码', ha='center', va='center', transform=axes[1,2].transAxes)
    axes[1,2].set_title('CKD分层预测特征重要性 (Top 10)', fontsize=12)

plt.tight_layout()
plt.show()
```

```python
# 生成预测示例
print("=== 预测示例 ===")

# 检查所需变量是否存在
if ('stage_performance' in locals() or 'stage_performance' in globals()) and \
   ('rate_performance' in locals() or 'rate_performance' in globals()) and \
   ('stage_results' in locals() or 'stage_results' in globals()) and \
   ('rate_results' in locals() or 'rate_results' in globals()):

    print("使用最佳模型对测试集前5个样本进行预测:")

    # 获取最佳模型
    best_stage_model_name = stage_performance.loc[stage_performance['Accuracy'].idxmax(), 'Model']
    best_rate_model_name = rate_performance.loc[rate_performance['Accuracy'].idxmax(), 'Model']

    best_stage_pred = stage_results[best_stage_model_name]['y_pred'][:5]
    best_rate_pred = rate_results[best_rate_model_name]['y_pred'][:5]

    actual_stage = y_test_stage.iloc[:5].values
    actual_rate = y_test_rate.iloc[:5].values

    prediction_df = pd.DataFrame({
        '样本': range(1, 6),
        '实际CKD分期': actual_stage,
        '预测CKD分期': best_stage_pred,
        '实际CKD分层': actual_rate,
        '预测CKD分层': best_rate_pred
    })

    print(prediction_df)

    # 计算预测准确性
    stage_correct = sum(actual_stage == best_stage_pred)
    rate_correct = sum(actual_rate == best_rate_pred)
    print(f"\n前5个样本预测准确性:")
    print(f"CKD分期: {stage_correct}/5 ({stage_correct/5*100:.1f}%)")
    print(f"CKD分层: {rate_correct}/5 ({rate_correct/5*100:.1f}%)")

else:
    print("预测示例需要先运行以下部分的代码:")
    if not ('stage_performance' in locals() or 'stage_performance' in globals()):
        print("- 第七部分：CKD分期预测模型训练与评估")
    if not ('rate_performance' in locals() or 'rate_performance' in globals()):
        print("- 第八部分：CKD分层预测模型训练与评估")
    if not ('stage_results' in locals() or 'stage_results' in globals()):
        print("- 第七部分：CKD分期预测模型结果")
    if not ('rate_results' in locals() or 'rate_results' in globals()):
        print("- 第八部分：CKD分层预测模型结果")
```

```python
# 保存模型和结果
import pickle

print("=== 保存模型和结果 ===")

# 检查所需变量是否存在
if ('stage_results' in locals() or 'stage_results' in globals()) and \
   ('rate_results' in locals() or 'rate_results' in globals()) and \
   ('stage_performance' in locals() or 'stage_performance' in globals()) and \
   ('rate_performance' in locals() or 'rate_performance' in globals()):

    # 获取最佳模型名称
    best_stage_model_name = stage_performance.loc[stage_performance['Accuracy'].idxmax(), 'Model']
    best_rate_model_name = rate_performance.loc[rate_performance['Accuracy'].idxmax(), 'Model']

    # 保存最佳模型
    best_models = {
        'stage_model': stage_results[best_stage_model_name]['model'],
        'rate_model': rate_results[best_rate_model_name]['model'],
        'scaler': scaler,
        'feature_cols': feature_cols
    }

    with open('ckd_prediction_models.pkl', 'wb') as f:
        pickle.dump(best_models, f)

    # 保存分析结果
    analysis_results = {
        'stage_performance': stage_performance,
        'rate_performance': rate_performance,
        'data_summary': {
            'total_samples': len(df),
            'num_features': len(feature_cols),
            'stage_distribution': dict(df['stage'].value_counts().sort_index()),
            'rate_distribution': dict(df['rate'].value_counts().sort_index())
        }
    }

    # 添加特征重要性（如果存在）
    if 'feature_importance_stage' in locals() or 'feature_importance_stage' in globals():
        analysis_results['feature_importance_stage'] = feature_importance_stage
    if 'feature_importance_rate' in locals() or 'feature_importance_rate' in globals():
        analysis_results['feature_importance_rate'] = feature_importance_rate

    with open('ckd_analysis_results.pkl', 'wb') as f:
        pickle.dump(analysis_results, f)

    print("模型和结果已保存:")
    print("- ckd_prediction_models.pkl: 训练好的模型")
    print("- ckd_analysis_results.pkl: 分析结果")

else:
    print("保存模型需要先运行以下部分的代码:")
    if not ('stage_results' in locals() or 'stage_results' in globals()):
        print("- 第七部分：CKD分期预测模型训练与评估")
    if not ('rate_results' in locals() or 'rate_results' in globals()):
        print("- 第八部分：CKD分层预测模型训练与评估")
    print("建议按顺序运行所有代码段以获得完整结果")
```

```python
print("=== 分析完成 ===")
print("慢性肾病状态预测分析已完成！")
print("\n主要发现:")
print("1. 成功构建了CKD分期和分层的预测模型")
print("2. 识别了影响CKD状态的关键因素")
print("3. 为临床决策提供了数据支持")
print("\n建议:")
print("1. 重点关注特征重要性排名靠前的指标")
print("2. 定期监测高风险患者的关键指标")
print("3. 结合临床经验优化预测模型")
```
