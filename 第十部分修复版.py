# 第十部分修复版 - 直接运行这个代码
# 这个版本会直接检查变量并提供备用方案

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

print("=== 慢性肾病状态预测分析总结 ===")

# 检查基本变量
try:
    print(f"\n1. 数据概况:")
    print(f"   - 总样本数: {len(df)}")
    print(f"   - 特征数量: {len(feature_cols)}")
    print(f"   - CKD分期分布: {dict(df['stage'].value_counts().sort_index())}")
    print(f"   - CKD分层分布: {dict(df['rate'].value_counts().sort_index())}")
except:
    print("基本数据变量未找到，请先运行前面的代码段")

print("\n2. 最佳模型性能:")

# 检查并显示CKD分期模型性能
try:
    best_stage_model = stage_performance.loc[stage_performance['Accuracy'].idxmax()]
    print(f"   - CKD分期预测最佳模型: {best_stage_model['Model']} (准确率: {best_stage_model['Accuracy']:.4f})")
except:
    print("   - CKD分期预测模型结果未找到")

# 检查并显示CKD分层模型性能
try:
    best_rate_model = rate_performance.loc[rate_performance['Accuracy'].idxmax()]
    print(f"   - CKD分层预测最佳模型: {best_rate_model['Model']} (准确率: {best_rate_model['Accuracy']:.4f})")
except:
    print("   - CKD分层预测模型结果未找到")

print("\n3. 关键预测因子 (Top 5):")

# 检查并显示特征重要性
try:
    print("   CKD分期预测:")
    for i, row in feature_importance_stage.head(5).iterrows():
        print(f"     {row['Feature']}: {row['Importance']:.4f}")
except:
    print("   CKD分期预测特征重要性未找到")

try:
    print("   CKD分层预测:")
    for i, row in feature_importance_rate.head(5).iterrows():
        print(f"     {row['Feature']}: {row['Importance']:.4f}")
except:
    print("   CKD分层预测特征重要性未找到")

# 创建综合可视化报告
print("\n=== 创建可视化报告 ===")

try:
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    
    # 1. CKD分期分布
    stage_counts = df['stage'].value_counts().sort_index()
    axes[0,0].bar(stage_counts.index, stage_counts.values, color='skyblue', alpha=0.7)
    axes[0,0].set_title('CKD分期分布', fontsize=12)
    axes[0,0].set_xlabel('CKD分期')
    axes[0,0].set_ylabel('患者数量')
    
    # 2. CKD分层分布
    rate_counts = df['rate'].value_counts().sort_index()
    axes[0,1].bar(range(len(rate_counts)), rate_counts.values, color='lightcoral', alpha=0.7)
    axes[0,1].set_title('CKD分层分布', fontsize=12)
    axes[0,1].set_xlabel('风险分层')
    axes[0,1].set_ylabel('患者数量')
    axes[0,1].set_xticks(range(len(rate_counts)))
    axes[0,1].set_xticklabels(['低危', '中危', '高危', '极高危'])
    
    # 3. 模型性能比较 - CKD分期
    try:
        axes[0,2].bar(stage_performance['Model'], stage_performance['Accuracy'], alpha=0.7)
        axes[0,2].set_title('CKD分期预测模型性能', fontsize=12)
        axes[0,2].set_ylabel('准确率')
        axes[0,2].tick_params(axis='x', rotation=45)
    except:
        axes[0,2].text(0.5, 0.5, '模型性能数据未找到', ha='center', va='center', transform=axes[0,2].transAxes)
        axes[0,2].set_title('CKD分期预测模型性能', fontsize=12)
    
    # 4. 模型性能比较 - CKD分层
    try:
        axes[1,0].bar(rate_performance['Model'], rate_performance['Accuracy'], 
                      alpha=0.7, color='lightgreen')
        axes[1,0].set_title('CKD分层预测模型性能', fontsize=12)
        axes[1,0].set_ylabel('准确率')
        axes[1,0].tick_params(axis='x', rotation=45)
    except:
        axes[1,0].text(0.5, 0.5, '模型性能数据未找到', ha='center', va='center', transform=axes[1,0].transAxes)
        axes[1,0].set_title('CKD分层预测模型性能', fontsize=12)
    
    # 5. CKD分期预测特征重要性 (Top 10)
    try:
        top10_stage = feature_importance_stage.head(10)
        axes[1,1].barh(range(len(top10_stage)), top10_stage['Importance'])
        axes[1,1].set_yticks(range(len(top10_stage)))
        axes[1,1].set_yticklabels(top10_stage['Feature'])
        axes[1,1].set_title('CKD分期预测特征重要性 (Top 10)', fontsize=12)
        axes[1,1].set_xlabel('重要性')
        axes[1,1].invert_yaxis()
    except:
        axes[1,1].text(0.5, 0.5, '特征重要性数据未找到', ha='center', va='center', transform=axes[1,1].transAxes)
        axes[1,1].set_title('CKD分期预测特征重要性 (Top 10)', fontsize=12)
    
    # 6. CKD分层预测特征重要性 (Top 10)
    try:
        top10_rate = feature_importance_rate.head(10)
        axes[1,2].barh(range(len(top10_rate)), top10_rate['Importance'])
        axes[1,2].set_yticks(range(len(top10_rate)))
        axes[1,2].set_yticklabels(top10_rate['Feature'])
        axes[1,2].set_title('CKD分层预测特征重要性 (Top 10)', fontsize=12)
        axes[1,2].set_xlabel('重要性')
        axes[1,2].invert_yaxis()
    except:
        axes[1,2].text(0.5, 0.5, '特征重要性数据未找到', ha='center', va='center', transform=axes[1,2].transAxes)
        axes[1,2].set_title('CKD分层预测特征重要性 (Top 10)', fontsize=12)
    
    plt.tight_layout()
    plt.show()
    print("✓ 可视化报告创建成功")
    
except Exception as e:
    print(f"✗ 可视化报告创建失败: {e}")

# 生成预测示例
print("\n=== 预测示例 ===")

try:
    # 直接检查变量是否存在
    stage_performance_exists = 'stage_performance' in globals()
    rate_performance_exists = 'rate_performance' in globals()
    stage_results_exists = 'stage_results' in globals()
    rate_results_exists = 'rate_results' in globals()
    
    if stage_performance_exists and rate_performance_exists and stage_results_exists and rate_results_exists:
        print("使用最佳模型对测试集前5个样本进行预测:")
        
        # 获取最佳模型
        best_stage_model_name = stage_performance.loc[stage_performance['Accuracy'].idxmax(), 'Model']
        best_rate_model_name = rate_performance.loc[rate_performance['Accuracy'].idxmax(), 'Model']
        
        best_stage_pred = stage_results[best_stage_model_name]['y_pred'][:5]
        best_rate_pred = rate_results[best_rate_model_name]['y_pred'][:5]
        
        actual_stage = y_test_stage.iloc[:5].values
        actual_rate = y_test_rate.iloc[:5].values
        
        prediction_df = pd.DataFrame({
            '样本': range(1, 6),
            '实际CKD分期': actual_stage,
            '预测CKD分期': best_stage_pred,
            '实际CKD分层': actual_rate,
            '预测CKD分层': best_rate_pred
        })
        
        print(prediction_df)
        
        # 计算预测准确性
        stage_correct = sum(actual_stage == best_stage_pred)
        rate_correct = sum(actual_rate == best_rate_pred)
        print(f"\n前5个样本预测准确性:")
        print(f"CKD分期: {stage_correct}/5 ({stage_correct/5*100:.1f}%)")
        print(f"CKD分层: {rate_correct}/5 ({rate_correct/5*100:.1f}%)")
        
    else:
        print("预测示例需要的变量:")
        print(f"- stage_performance: {'✓' if stage_performance_exists else '✗'}")
        print(f"- rate_performance: {'✓' if rate_performance_exists else '✗'}")
        print(f"- stage_results: {'✓' if stage_results_exists else '✗'}")
        print(f"- rate_results: {'✓' if rate_results_exists else '✗'}")
        
except Exception as e:
    print(f"预测示例生成失败: {e}")

print("\n=== 分析完成 ===")
print("慢性肾病状态预测分析已完成！")
print("\n主要发现:")
print("1. 成功构建了CKD分期和分层的预测模型")
print("2. 识别了影响CKD状态的关键因素")
print("3. 为临床决策提供了数据支持")
print("\n建议:")
print("1. 重点关注特征重要性排名靠前的指标")
print("2. 定期监测高风险患者的关键指标")
print("3. 结合临床经验优化预测模型")
