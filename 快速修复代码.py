# 快速修复代码 - 解决变量未定义问题
# 如果您遇到 NameError，请先运行这段代码

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("=== 快速修复：重新加载数据和训练模型 ===")

# 重新读取数据
try:
    data_clean = pd.read_csv('可参考选题1数据-慢性肾病状态预测/data_clean.csv')
    kidney_clean = pd.read_csv('可参考选题1数据-慢性肾病状态预测/kidney_clean.csv')
    print("✓ 数据加载成功")
except:
    print("✗ 数据加载失败，请检查文件路径")
    exit()

# 设置基本变量
df = data_clean.copy()
model_df = kidney_clean.copy()

# 准备特征和目标变量
feature_cols = [col for col in model_df.columns if col not in ['stage', 'rate']]
X = model_df[feature_cols]
y_stage = model_df['stage']
y_rate = model_df['rate']

# 数据标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 划分训练集和测试集
X_train_stage, X_test_stage, y_train_stage, y_test_stage = train_test_split(
    X_scaled, y_stage, test_size=0.3, random_state=42, stratify=y_stage
)

X_train_rate, X_test_rate, y_train_rate, y_test_rate = train_test_split(
    X_scaled, y_rate, test_size=0.3, random_state=42, stratify=y_rate
)

# 定义模型
models = {
    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
    'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
    'SVM': SVC(random_state=42, probability=True)
}

print("✓ 数据预处理完成")

# 快速训练CKD分期预测模型
print("\n=== 训练CKD分期预测模型 ===")
stage_results = {}

for name, model in models.items():
    print(f"训练 {name}...")
    model.fit(X_train_stage, y_train_stage)
    y_pred_stage = model.predict(X_test_stage)
    y_pred_proba_stage = model.predict_proba(X_test_stage)
    accuracy = accuracy_score(y_test_stage, y_pred_stage)
    
    stage_results[name] = {
        'model': model,
        'accuracy': accuracy,
        'y_pred': y_pred_stage,
        'y_pred_proba': y_pred_proba_stage
    }
    print(f"  准确率: {accuracy:.4f}")

# 创建性能比较表
stage_performance = pd.DataFrame({
    'Model': list(stage_results.keys()),
    'Accuracy': [result['accuracy'] for result in stage_results.values()]
})

print("\n=== 训练CKD分层预测模型 ===")
rate_results = {}

for name, model_class in [('Random Forest', RandomForestClassifier), 
                         ('Logistic Regression', LogisticRegression), 
                         ('SVM', SVC)]:
    print(f"训练 {name}...")
    if name == 'Random Forest':
        model = model_class(n_estimators=100, random_state=42)
    elif name == 'Logistic Regression':
        model = model_class(random_state=42, max_iter=1000)
    else:
        model = model_class(random_state=42, probability=True)
    
    model.fit(X_train_rate, y_train_rate)
    y_pred_rate = model.predict(X_test_rate)
    y_pred_proba_rate = model.predict_proba(X_test_rate)
    accuracy = accuracy_score(y_test_rate, y_pred_rate)
    
    rate_results[name] = {
        'model': model,
        'accuracy': accuracy,
        'y_pred': y_pred_rate,
        'y_pred_proba': y_pred_proba_rate
    }
    print(f"  准确率: {accuracy:.4f}")

# 创建性能比较表
rate_performance = pd.DataFrame({
    'Model': list(rate_results.keys()),
    'Accuracy': [result['accuracy'] for result in rate_results.values()]
})

# 特征重要性分析
print("\n=== 分析特征重要性 ===")
rf_stage_model = stage_results['Random Forest']['model']
rf_rate_model = rate_results['Random Forest']['model']

feature_importance_stage = pd.DataFrame({
    'Feature': feature_cols,
    'Importance': rf_stage_model.feature_importances_
}).sort_values('Importance', ascending=False)

feature_importance_rate = pd.DataFrame({
    'Feature': feature_cols,
    'Importance': rf_rate_model.feature_importances_
}).sort_values('Importance', ascending=False)

print("✓ 所有模型训练完成")
print("✓ 所有变量已定义，现在可以运行第十部分的代码了")

# 显示结果摘要
print("\n=== 结果摘要 ===")
print("CKD分期预测模型性能:")
print(stage_performance.sort_values('Accuracy', ascending=False))
print("\nCKD分层预测模型性能:")
print(rate_performance.sort_values('Accuracy', ascending=False))

print(f"\n最重要的5个特征 (CKD分期预测):")
for i, row in feature_importance_stage.head(5).iterrows():
    print(f"  {row['Feature']}: {row['Importance']:.4f}")

print(f"\n最重要的5个特征 (CKD分层预测):")
for i, row in feature_importance_rate.head(5).iterrows():
    print(f"  {row['Feature']}: {row['Importance']:.4f}")
